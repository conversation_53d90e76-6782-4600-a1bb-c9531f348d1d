from django.shortcuts import render
import pandas as pd
from datetime import date, timedelta
from apps.authentication.decorators import admin_required, staff_required
from django.contrib.auth.decorators import login_required
from plotly.offline import plot
import plotly.express as px
import plotly.graph_objects as go
from apps.genomom.models import SampleInfo
from django.http import HttpResponse
import io

# Define color schemes
COLOR_SCHEMES = {
    'primary': ['#2E3192', '#1BFFFF', '#D4145A', '#FBB03B', '#662D8C', '#ED1E79'],
    'secondary': ['#009245', '#FCEE21', '#00A8C5', '#D9E021', '#8A9B0F', '#EB6534'],
    'tertiary': ['#662D8C', '#ED1E79', '#FBB03B', '#D4145A', '#2E3192', '#1BFFFF'],
    'hospital': ['#1A237E', '#0D47A1', '#01579B', '#0277BD', '#0288D1', '#039BE5'],
    'regional': ['#004D40', '#00695C', '#00796B', '#00897B', '#009688', '#26A69A']
}

def export_to_excel(df, filename):
    """Helper function to export DataFrame to Excel"""
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, sheet_name='Sheet1', index=False)
    output.seek(0)
    response = HttpResponse(
        output.read(),
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename="{filename}"'
    return response

def generate_pie_chart(df, target_col, custom_colors=None, title=None):
    """Generate pie chart with export button"""
    temp_df = df.value_counts([target_col]).to_frame().reset_index()
    temp_df.columns = [target_col, 'Counts']

    fig = px.pie(temp_df, values="Counts", names=target_col, hole=0.5,
                 labels={target_col: f"Total Sample on {target_col}"},
                 color_discrete_sequence=custom_colors)

    fig.update_traces(textposition='outside',
                      textinfo='label+percent+value',
                      textfont_size=12)

    fig.update_layout(
        height=450,  # Fixed height for consistency
        margin=dict(l=20, r=20, t=20, b=20),  # Reduced top margin since we removed title
        showlegend=True,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )

    # Configure the chart to be more interactive
    config = {
        'displayModeBar': False,  # Remove the mode bar
        'responsive': True,
        'displaylogo': False,  # Remove the plotly logo
    }

    chart_div = plot(fig, output_type="div", config=config)
    return chart_div, temp_df

def generate_simple_bar_chart(df, target_col, limit=20):
    """Generate simple bar chart with top N entries"""
    # Count occurrences and get top N
    temp_df = df[target_col].value_counts().head(limit).reset_index()
    temp_df.columns = [target_col, 'Counts']

    # Create simple bar chart
    fig = go.Figure(data=[
        go.Bar(
            x=temp_df[target_col],
            y=temp_df['Counts'],
            text=temp_df['Counts'],
            textposition='auto',
            marker_color='#2E3192',
            hovertemplate=(
                f"<b>{target_col}:</b> %{{x}}<br>"
                f"<b>Count:</b> %{{y}}<br>"
                "<extra></extra>"
            )
        )
    ])

    # Update layout
    fig.update_layout(
        height=500,
        margin=dict(l=50, r=50, t=50, b=100),
        xaxis=dict(
            title=target_col.replace('_', ' ').title(),
            tickangle=45,
            showgrid=False
        ),
        yaxis=dict(
            title='Count',
            showgrid=True,
            gridcolor='#E5E5E5'
        ),
        plot_bgcolor='white',
        paper_bgcolor='white',
        showlegend=False
    )

    # Configure the chart
    config = {
        'displayModeBar': False,
        'responsive': True,
        'displaylogo': False
    }

    return plot(fig, output_type="div", config=config), temp_df

@login_required(login_url="/")
@staff_required
def all_db_info(request):
    """Main view for database information dashboard"""

    # Get all samples
    all_samples = SampleInfo.objects.all()
    samples = all_samples.values(
        "patient__jedan__jedan_name",
        "patient__jedan_branch__jedan_branch_name",
        "patient__hospital__hospital_name",
        "patient__hospital__do_address",
        "patient__hospital__si_address",
        "patient__doctor__full_name",
        "patient__patient_age",
        "patient__fetus_number",
        "service_type__service_name",
        "patient__ivf_treatment",
        "entry_date",
        "report_publish",
        "received_by",
        "test_type",
        "is_active",
        "samples_trisomy__final_result2"
    )

    # Convert to DataFrame
    df = pd.DataFrame(samples)
    df.rename(columns={
        'patient__jedan__jedan_name': "jedan",
        'patient__jedan_branch__jedan_branch_name': "jedan_branch",
        'patient__hospital__hospital_name': "hospital",
        "patient__hospital__do_address": "do_address",
        "patient__hospital__si_address": "si_address",
        "patient__doctor__full_name": "doctor",
        'patient__patient_age': 'age',
        'patient__fetus_number': "fetus",
        "patient__ivf_treatment": "ivf_treatment",
        "service_type__service_name": "service_type",
        "samples_trisomy__final_result2": "final_result",
    }, inplace=True)

    # Add counts column
    df["Counts"] = 1

    # Clean and transform data
    df["final_result"] = df["final_result"].replace({
        '0': 'Low Risk',
        '1': 'HIGH Risk',
        '2': 'Borderline',
        '3': 'Retest_P',
        '4': 'Retest_L',
        '5': 'Retest_S',
        '6': 'Re_draw',
        '7': '검사불능',
    })

    df["ivf_treatment"] = df["ivf_treatment"].replace({
        "1": "유",
        "0": "무",
        '-': '-'
    })

    # Combine columns for better visualization
    df["jedan_branch"] = df["jedan"] + '-' + df["jedan_branch"]
    df["hospital"] = df["jedan"] + '-' + df["hospital"]

    # Calculate statistics for header cards
    total_samples = len(df)
    active_samples = len(df[df['is_active'] == True])
    high_risk_samples = len(df[df['final_result'] == 'HIGH Risk'])
    ivf_samples = len(df[df['ivf_treatment'] == '유'])

    # Generate visualizations with export data
    # Service Type
    service_type_div, service_type_df = generate_pie_chart(
        df, "service_type", COLOR_SCHEMES['primary'])

    # Fetus
    fetus_div, fetus_df = generate_pie_chart(
        df, "fetus", COLOR_SCHEMES['secondary'])

    # Test Type
    test_type_div, test_type_df = generate_pie_chart(
        df, "test_type", COLOR_SCHEMES['tertiary'])

    # Jedan
    jedan_div, jedan_df = generate_pie_chart(
        df, "jedan", COLOR_SCHEMES['primary'])

    # Final Result
    final_result_div, final_result_df = generate_pie_chart(
        df, "final_result", COLOR_SCHEMES['secondary'])

    # IVF Treatment
    ivf_treatment_div, ivf_treatment_df = generate_pie_chart(
        df, "ivf_treatment", COLOR_SCHEMES['tertiary'])

    # Active Status
    is_active_div, is_active_df = generate_pie_chart(
        df, "is_active", COLOR_SCHEMES['primary'])

    # Generate simple bar charts
    jedan_branch_div, jedan_branch_df = generate_simple_bar_chart(df, "jedan_branch", limit=20)
    hospital_div, hospital_df = generate_simple_bar_chart(df, "hospital", limit=20)
    do_address_div, do_address_df = generate_simple_bar_chart(df, "do_address", limit=15)
    si_address_div, si_address_df = generate_simple_bar_chart(df, "si_address", limit=15)
    doctor_div, doctor_df = generate_simple_bar_chart(df, "doctor", limit=20)

    # Handle export requests
    if request.GET.get('export'):
        export_type = request.GET.get('export')
        if export_type == 'service_type':
            return export_to_excel(service_type_df, 'service_type_distribution.xlsx')
        elif export_type == 'fetus':
            return export_to_excel(fetus_df, 'fetus_distribution.xlsx')
        elif export_type == 'test_type':
            return export_to_excel(test_type_df, 'test_type_distribution.xlsx')
        elif export_type == 'jedan':
            return export_to_excel(jedan_df, 'jedan_distribution.xlsx')
        elif export_type == 'final_result':
            return export_to_excel(final_result_df, 'final_result_distribution.xlsx')
        elif export_type == 'ivf_treatment':
            return export_to_excel(ivf_treatment_df, 'ivf_treatment_distribution.xlsx')
        elif export_type == 'is_active':
            return export_to_excel(is_active_df, 'active_status_distribution.xlsx')
        elif export_type == 'jedan_branch':
            return export_to_excel(jedan_branch_df, 'jedan_branch_distribution.xlsx')
        elif export_type == 'hospital':
            return export_to_excel(hospital_df, 'hospital_distribution.xlsx')
        elif export_type == 'do_address':
            return export_to_excel(do_address_df, 'do_address_distribution.xlsx')
        elif export_type == 'si_address':
            return export_to_excel(si_address_df, 'si_address_distribution.xlsx')
        elif export_type == 'doctor':
            return export_to_excel(doctor_df, 'doctor_distribution.xlsx')

    context = {
        # Statistics for header cards
        "total_samples": total_samples,
        "active_samples": active_samples,
        "high_risk_samples": high_risk_samples,
        "ivf_samples": ivf_samples,

        # Visualizations
        "service_type_div": service_type_div,
        "fetus_div": fetus_div,
        "test_type_div": test_type_div,
        "jedan_div": jedan_div,
        "final_result_div": final_result_div,
        "is_active_div": is_active_div,
        "ivf_treatment_div": ivf_treatment_div,

        # Bar plots
        "jedan_branch_div": jedan_branch_div,
        "hospital_div": hospital_div,
        "do_address_div": do_address_div,
        "si_address_div": si_address_div,
        "doctor_div": doctor_div,

        "active_menu": "dashboard",
    }

    template = "dashboards/all_db_info.html"
    return render(request, template_name=template, context=context)
