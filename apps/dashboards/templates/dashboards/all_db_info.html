{% extends "layouts/base.html" %}

{% block title %} Database Analytics Dashboard {% endblock %}

{% block stylesheets %}
<style>
    .card {
        margin-bottom: 1.5rem;
        box-shadow: 0 0 2rem 0 rgba(136, 152, 170, .15);
        border: none;
    }
    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 1.5rem;
        background: #f8f9fe;
        border-bottom: 1px solid #e9ecef;
    }
    .card-header h5 {
        margin: 0;
        font-weight: 600;
        color: #2E3192;
    }
    .export-btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        background: #2E3192;
        border: none;
        color: white;
        transition: all 0.2s;
    }
    .export-btn:hover {
        background: #1a1d5e;
        color: white;
    }
    .stat-card {
        transition: transform 0.2s;
        border: none;
        box-shadow: 0 0 2rem 0 rgba(136, 152, 170, .15);
    }
    .stat-card:hover {
        transform: translateY(-5px);
    }
    .chart-container {
        position: relative;
        padding: 1rem;
        height: 450px;
    }
    .bar-chart-container {
        position: relative;
        padding: 1rem;
        height: 600px;
        margin-bottom: 2rem;
    }
    .header-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #2E3192;
        margin: 0;
    }
    .header-subtitle {
        font-size: 0.875rem;
        color: #8898aa;
        margin: 0;
    }
    .stat-icon {
        width: 3rem;
        height: 3rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background: #2E3192;
    }
    .stat-value {
        font-size: 1.5rem;
        font-weight: 600;
        color: #2E3192;
    }
    .stat-label {
        font-size: 0.875rem;
        color: #8898aa;
        text-transform: uppercase;
        letter-spacing: 0.025em;
    }
    .js-plotly-plot {
        width: 100% !important;
        height: 100% !important;
    }
    .plot-container {
        height: 100% !important;
    }
    .main-svg {
        height: 100% !important;
    }
    .xtick {
        transform: rotate(45deg);
        text-anchor: start;
        dominant-baseline: hanging;
    }
    .card-body {
        position: relative;
        padding: 1.5rem;
    }
    .plotly-graph-div {
        width: 100% !important;
        height: 100% !important;
    }
    /* Improved legend styling */
    .legend {
        background: rgba(255, 255, 255, 0.8);
        border-radius: 4px;
        padding: 5px;
    }
    /* Improved hover tooltip */
    .hovertext {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
</style>
{% endblock stylesheets %}

{% block content %}
<div class="header bg-primary pb-6">
    {% include 'genomom/messages_display.html'%}
    
    <div class="container-fluid">
        <div class="header-body">
            <div class="row align-items-center py-4">
                <div class="col-lg-6 col-7">
                    <h6 class="h2 text-white d-inline-block mb-0">Database Analytics</h6>
                    <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                        <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                            <li class="breadcrumb-item"><a href="#"><i class="fas fa-home"></i></a></li>
                            <li class="breadcrumb-item"><a href="#">Analytics</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Database Overview</li>
                        </ol>
                    </nav>
                </div>
            </div>
            
            <!-- Statistics Cards -->
            <div class="row">
                <div class="col-xl-3 col-md-6">
                    <div class="card card-stats stat-card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <h5 class="stat-label">Total Samples</h5>
                                    <span class="stat-value">{{ total_samples }}</span>
                                </div>
                                <div class="col-auto">
                                    <div class="stat-icon">
                                        <i class="ni ni-chart-bar-32 text-white"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6">
                    <div class="card card-stats stat-card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <h5 class="stat-label">Active Samples</h5>
                                    <span class="stat-value">{{ active_samples }}</span>
                                </div>
                                <div class="col-auto">
                                    <div class="stat-icon">
                                        <i class="ni ni-check-bold text-white"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6">
                    <div class="card card-stats stat-card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <h5 class="stat-label">High Risk Cases</h5>
                                    <span class="stat-value">{{ high_risk_samples }}</span>
                                </div>
                                <div class="col-auto">
                                    <div class="stat-icon">
                                        <i class="ni ni-notification-70 text-white"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6">
                    <div class="card card-stats stat-card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <h5 class="stat-label">IVF Treatments</h5>
                                    <span class="stat-value">{{ ivf_samples }}</span>
                                </div>
                                <div class="col-auto">
                                    <div class="stat-icon">
                                        <i class="ni ni-single-02 text-white"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Page content -->
<div class="container-fluid mt--6">
    <!-- Service Type and Fetus Distribution -->
    <div class="row">
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="h3 mb-0"><i class="fa-solid fa-vial-circle-check"></i> Service Type Distribution</h5>
                    <a href="?export=service_type" class="btn btn-sm export-btn">
                        <i class="fas fa-download"></i> Export
                    </a>
                </div>
                <div class="card-body">
                    {% autoescape off %}
                    {{ service_type_div }}
                    {% endautoescape %}
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="h3 mb-0"><i class="fa-solid fa-baby"></i> Fetus Distribution</h5>
                    <a href="?export=fetus" class="btn btn-sm export-btn">
                        <i class="fas fa-download"></i> Export
                    </a>
                </div>
                <div class="card-body">
                    {% autoescape off %}
                    {{ fetus_div }}
                    {% endautoescape %}
                </div>
            </div>
        </div>
    </div>

    <!-- Test Type and Jedan Distribution -->
    <div class="row">
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="h3 mb-0"><i class="fa-solid fa-vial-circle-check"></i> Test Type Distribution</h5>
                    <a href="?export=test_type" class="btn btn-sm export-btn">
                        <i class="fas fa-download"></i> Export
                    </a>
                </div>
                <div class="card-body">
                    {% autoescape off %}
                    {{ test_type_div }}
                    {% endautoescape %}
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="h3 mb-0"><i class="fa-solid fa-building"></i> Jedan Distribution</h5>
                    <a href="?export=jedan" class="btn btn-sm export-btn">
                        <i class="fas fa-download"></i> Export
                    </a>
                </div>
                <div class="card-body">
                    {% autoescape off %}
                    {{ jedan_div }}
                    {% endautoescape %}
                </div>
            </div>
        </div>
    </div>

    <!-- Result Distribution and Regional Distribution -->
    <div class="row">
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="h3 mb-0"><i class="fa-solid fa-square-poll-vertical"></i> Result Distribution</h5>
                    <a href="?export=final_result" class="btn btn-sm export-btn">
                        <i class="fas fa-download"></i> Export
                    </a>
                </div>
                <div class="card-body">
                    {% autoescape off %}
                    {{ final_result_div }}
                    {% endautoescape %}
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="h3 mb-0"><i class="fa-solid fa-map-marker-alt"></i> Regional Distribution</h5>
                    <a href="?export=regional" class="btn btn-sm export-btn">
                        <i class="fas fa-download"></i> Export
                    </a>
                </div>
                <div class="card-body">
                    {% autoescape off %}
                    {{ regional_div }}
                    {% endautoescape %}
                </div>
            </div>
        </div>
    </div>

    <!-- IVF Treatment and Active Status -->
    <div class="row">
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="h3 mb-0"><i class="fa-solid fa-square-poll-vertical"></i> IVF Treatment Distribution</h5>
                    <a href="?export=ivf_treatment" class="btn btn-sm export-btn">
                        <i class="fas fa-download"></i> Export
                    </a>
                </div>
                <div class="card-body">
                    {% autoescape off %}
                    {{ ivf_treatment_div }}
                    {% endautoescape %}
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="h3 mb-0"><i class="fa-solid fa-square-poll-vertical"></i> Active Status Distribution</h5>
                    <a href="?export=is_active" class="btn btn-sm export-btn">
                        <i class="fas fa-download"></i> Export
                    </a>
                </div>
                <div class="card-body">
                    {% autoescape off %}
                    {{ is_active_div }}
                    {% endautoescape %}
                </div>
            </div>
        </div>
    </div>

    <!-- Bar Charts -->
    <div class="card">
        <div class="card-header">
            <h5 class="h3 mb-0"><i class="fa-sharp fa-solid fa-hospital"></i> Top 30 Jedan Branch Distribution</h5>
            <a href="?export=jedan_branch" class="btn btn-sm export-btn">
                <i class="fas fa-download"></i> Export
            </a>
        </div>
        <div class="card-body bar-chart-container">
            {{ jedan_branch_div | safe }}
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h5 class="h3 mb-0"><i class="fa-sharp fa-solid fa-hospital"></i> Top 30 Hospital-Doctor Distribution</h5>
            <a href="?export=hospital_doctor" class="btn btn-sm export-btn">
                <i class="fas fa-download"></i> Export
            </a>
        </div>
        <div class="card-body bar-chart-container">
            {{ hospital_doctor_div | safe }}
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h5 class="h3 mb-0"><i class="fa-sharp fa-solid fa-hospital"></i> Top 30 Regional Distribution</h5>
            <a href="?export=do_jedan" class="btn btn-sm export-btn">
                <i class="fas fa-download"></i> Export
            </a>
        </div>
        <div class="card-body bar-chart-container">
            {{ do_jedan_div | safe }}
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h5 class="h3 mb-0"><i class="fa-sharp fa-solid fa-hospital"></i> Top 30 City Distribution</h5>
            <a href="?export=si_jedan" class="btn btn-sm export-btn">
                <i class="fas fa-download"></i> Export
            </a>
        </div>
        <div class="card-body bar-chart-container">
            {{ si_jedan_div | safe }}
        </div>
    </div>

    <div class="page-header-title">
        <div class='p-3 mb-2 bg-success text-white text-center h5 font-weight-bold'>
            <h3>Developed By Krishna (R&D Genomecare)</h3>
            <p class="mb-0">For any queries, please contact the AIM Team</p>
        </div>
    </div>
    
    {% include 'includes/footer.html' %}
</div>
{% endblock content %}

{% block javascripts %}
<script>
    // Add any custom JavaScript here if needed
</script>
{% endblock javascripts %}